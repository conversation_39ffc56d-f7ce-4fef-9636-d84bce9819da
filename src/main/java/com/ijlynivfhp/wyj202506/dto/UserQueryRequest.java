package com.ijlynivfhp.wyj202506.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户查询请求DTO
 */
@Data
public class UserQueryRequest {

    /**
     * 用户名（模糊查询）
     */
    private String username;

    /**
     * 邮箱（模糊查询）
     */
    private String email;

    /**
     * 手机号（模糊查询）
     */
    private String phone;

    /**
     * 真实姓名（模糊查询）
     */
    private String realName;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 页码（从1开始）
     */
    private Integer current = 1;

    /**
     * 每页大小
     */
    private Integer size = 10;

    /**
     * 排序字段
     */
    private String sortField = "createTime";

    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String sortOrder = "desc";

    /**
     * 获取偏移量
     */
    public int getOffset() {
        return (current - 1) * size;
    }

    /**
     * 验证并修正分页参数
     */
    public void validateAndFix() {
        if (current == null || current < 1) {
            current = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        if (size > 100) {
            size = 100; // 限制最大页面大小
        }
        if (sortField == null || sortField.trim().isEmpty()) {
            sortField = "createTime";
        }
        if (sortOrder == null || (!sortOrder.equalsIgnoreCase("asc") && !sortOrder.equalsIgnoreCase("desc"))) {
            sortOrder = "desc";
        }
    }
}
