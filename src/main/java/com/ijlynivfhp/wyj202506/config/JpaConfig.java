package com.ijlynivfhp.wyj202506.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * JPA配置类
 */
@Configuration
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = "com.ijlynivfhp.wyj202506.repository")
@EnableTransactionManagement
public class JpaConfig {
    
    // JPA审计功能已通过@EnableJpaAuditing启用
    // 这将自动填充@CreatedDate和@LastModifiedDate注解的字段
    
}
