package com.ijlynivfhp.wyj202506.controller;

import com.ijlynivfhp.wyj202506.common.PageResult;
import com.ijlynivfhp.wyj202506.common.Result;
import com.ijlynivfhp.wyj202506.dto.*;
import com.ijlynivfhp.wyj202506.service.UserService;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Validated
public class UserController {

    private final UserService userService;

    /**
     * 创建用户
     */
    @PostMapping
    public Result<UserDTO> createUser(@Valid @RequestBody UserCreateRequest request) {
        log.info("创建用户请求: {}", request.getUsername());
        UserDTO userDTO = userService.createUser(request);
        return Result.success("用户创建成功", userDTO);
    }

    /**
     * 根据ID获取用户
     */
    @GetMapping("/{id}")
    public Result<UserDTO> getUserById(@PathVariable @NotNull(message = "用户ID不能为空") Long id) {
        log.debug("获取用户详情: {}", id);
        UserDTO userDTO = userService.getUserById(id);
        return Result.success(userDTO);
    }



    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    public Result<UserDTO> updateUser(@PathVariable @NotNull(message = "用户ID不能为空") Long id,
                                      @Valid @RequestBody UserUpdateRequest request) {
        log.info("更新用户请求: {}", id);
        UserDTO userDTO = userService.updateUser(id, request);
        return Result.success("用户更新成功", userDTO);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteUser(@PathVariable @NotNull(message = "用户ID不能为空") Long id) {
        log.info("删除用户请求: {}", id);
        userService.deleteUser(id);
        return Result.<Void>success("用户删除成功", null);
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping("/batch")
    public Result<Void> deleteUsers(@RequestBody @NotEmpty(message = "用户ID列表不能为空") List<Long> ids) {
        log.info("批量删除用户请求: {}", ids);
        userService.deleteUsers(ids);
        return Result.<Void>success("用户批量删除成功", null);
    }

    /**
     * 分页查询用户
     */
    @GetMapping
    public Result<PageResult<UserDTO>> queryUsers(@Valid UserQueryRequest request) {
        log.debug("分页查询用户: {}", request);
        PageResult<UserDTO> pageResult = userService.queryUsers(request);
        return Result.success(pageResult);
    }

    /**
     * 获取所有用户
     */
    @GetMapping("/all")
    public Result<List<UserDTO>> getAllUsers() {
        log.debug("获取所有用户");
        List<UserDTO> users = userService.getAllUsers();
        return Result.success(users);
    }



    /**
     * 启用用户
     */
    @PutMapping("/{id}/enable")
    public Result<Void> enableUser(@PathVariable @NotNull(message = "用户ID不能为空") Long id) {
        log.info("启用用户: {}", id);
        userService.enableUser(id);
        return Result.<Void>success("用户启用成功", null);
    }

    /**
     * 禁用用户
     */
    @PutMapping("/{id}/disable")
    public Result<Void> disableUser(@PathVariable @NotNull(message = "用户ID不能为空") Long id) {
        log.info("禁用用户: {}", id);
        userService.disableUser(id);
        return Result.<Void>success("用户禁用成功", null);
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check/username")
    public Result<Boolean> checkUsername(@RequestParam @NotEmpty(message = "用户名不能为空") String username) {
        log.debug("检查用户名是否存在: {}", username);
        boolean exists = userService.existsByUsername(username);
        return Result.success(exists);
    }



    /**
     * 修改密码
     */
    @PutMapping("/{id}/password")
    public Result<Void> changePassword(@PathVariable @NotNull(message = "用户ID不能为空") Long id,
                                       @RequestBody @Valid ChangePasswordRequest request) {
        log.info("修改密码: {}", id);
        userService.changePassword(id, request.getOldPassword(), request.getNewPassword());
        return Result.<Void>success("密码修改成功", null);
    }

    /**
     * 重置密码
     */
    @PutMapping("/{id}/reset-password")
    public Result<Void> resetPassword(@PathVariable @NotNull(message = "用户ID不能为空") Long id,
                                      @RequestBody @Valid ResetPasswordRequest request) {
        log.info("重置密码: {}", id);
        userService.resetPassword(id, request.getNewPassword());
        return Result.<Void>success("密码重置成功", null);
    }

    /**
     * 统计用户数量
     */
    @GetMapping("/count")
    public Result<Long> countUsers() {
        log.debug("统计用户数量");
        long count = userService.countUsers();
        return Result.success(count);
    }


}
