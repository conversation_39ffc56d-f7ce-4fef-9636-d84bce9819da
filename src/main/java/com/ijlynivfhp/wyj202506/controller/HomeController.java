package com.ijlynivfhp.wyj202506.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 首页控制器
 */
@Controller
public class HomeController {

    /**
     * 首页
     */
    @GetMapping("/")
    @ResponseBody
    public String home() {
        return "<html><head><title>WYJ202506 管理系统</title></head><body>" +
               "<h1>🎉 WYJ202506 管理系统</h1>" +
               "<h2>📋 可用的API端点：</h2>" +
               "<ul>" +
               "<li><a href='/wyj202506/api/users/all'>获取所有用户</a></li>" +
               "<li><a href='/wyj202506/api/users?current=1&size=10'>分页查询用户</a></li>" +
               "<li><a href='/wyj202506/api/users/check/username?username=admin'>检查用户名是否存在</a></li>" +
               "<li><a href='/wyj202506/api/users/count'>统计用户数量</a></li>" +
               "</ul>" +
               "<h2>📖 API文档：</h2>" +
               "<p>基础URL: <code>http://localhost:8888/wyj202506/api/users</code></p>" +
               "<h3>支持的操作：</h3>" +
               "<ul>" +
               "<li><strong>GET</strong> /api/users/all - 获取所有用户</li>" +
               "<li><strong>GET</strong> /api/users/{id} - 根据ID获取用户</li>" +
               "<li><strong>GET</strong> /api/users - 分页查询用户</li>" +
               "<li><strong>POST</strong> /api/users - 创建用户</li>" +
               "<li><strong>PUT</strong> /api/users/{id} - 更新用户</li>" +
               "<li><strong>DELETE</strong> /api/users/{id} - 删除用户</li>" +
               "<li><strong>PUT</strong> /api/users/{id}/enable - 启用用户</li>" +
               "<li><strong>PUT</strong> /api/users/{id}/disable - 禁用用户</li>" +
               "</ul>" +
               "<h3>📝 创建用户示例：</h3>" +
               "<pre>" +
               "POST /wyj202506/api/users\n" +
               "Content-Type: application/json\n\n" +
               "{\n" +
               "  \"username\": \"testuser\",\n" +
               "  \"password\": \"123456\",\n" +
               "  \"email\": \"<EMAIL>\",\n" +
               "  \"realName\": \"测试用户\"\n" +
               "}" +
               "</pre>" +
               "</body></html>";
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @ResponseBody
    public String health() {
        return "OK";
    }
}
