package com.ijlynivfhp.wyj202506.controller;

import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;

@Controller
public class RSocketController {

    /**
     * Request-Response 模式
     * 客户端发送一个请求，服务器返回一个响应
     */
    @MessageMapping("request-response")
    public Mono<String> requestResponse(String request) {
        return Mono.just("Echo: " + request + " at " + LocalDateTime.now());
    }

    /**
     * Request-Stream 模式
     * 客户端发送一个请求，服务器返回一个数据流
     */
    @MessageMapping("request-stream")
    public Flux<String> requestStream(String request) {
        return Flux.interval(Duration.ofSeconds(1))
                .map(i -> "Stream response " + i + " for request: " + request + " at " + LocalDateTime.now())
                .take(10); // 限制为10个响应
    }

    /**
     * Fire-and-Forget 模式
     * 客户端发送一个请求，不期望响应
     */
    @MessageMapping("fire-and-forget")
    public Mono<Void> fireAndForget(String request) {
        System.out.println("Received fire-and-forget message: " + request + " at " + LocalDateTime.now());
        return Mono.empty();
    }

    /**
     * Channel 模式
     * 双向流通信
     */
    @MessageMapping("channel")
    public Flux<String> channel(Flux<String> requests) {
        return requests
                .map(request -> "Channel response for: " + request + " at " + LocalDateTime.now())
                .doOnNext(response -> System.out.println("Sending: " + response));
    }
}
