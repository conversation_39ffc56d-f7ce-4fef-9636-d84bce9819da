package com.ijlynivfhp.wyj202506.controller;

import io.rsocket.RSocket;
import io.rsocket.core.RSocketConnector;
import io.rsocket.transport.netty.client.TcpClientTransport;
import io.rsocket.util.DefaultPayload;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RSocket测试控制器
 * 通过HTTP接口测试RSocket功能，可以用Postman调用
 */
@RestController
@RequestMapping("/api/rsocket-test")
public class RSocketTestController {

    private RSocket getRSocketClient() {
        return RSocketConnector.create()
                .connect(TcpClientTransport.create("localhost", 7000))
                .block();
    }

    /**
     * 测试Request-Response模式
     * POST /api/rsocket-test/request-response
     * Body: {"message": "Hello RSocket"}
     */
    @PostMapping("/request-response")
    public Mono<String> testRequestResponse(@RequestBody TestMessage request) {
        RSocket socket = getRSocketClient();

        return socket.requestResponse(DefaultPayload.create(
                request.getMessage().getBytes(),
                "request-response".getBytes()
        ))
                .map(payload -> payload.getDataUtf8())
                .doFinally(signalType -> socket.dispose());
    }

    /**
     * 测试Request-Stream模式
     * POST /api/rsocket-test/request-stream
     * Body: {"message": "Stream Request", "count": 5}
     */
    @PostMapping("/request-stream")
    public Mono<List<String>> testRequestStream(@RequestBody StreamTestMessage request) {
        RSocket socket = getRSocketClient();

        return socket.requestStream(DefaultPayload.create(
                request.getMessage().getBytes(),
                "request-stream".getBytes()
        ))
                .map(payload -> payload.getDataUtf8())
                .take(request.getCount() != null ? request.getCount() : 5)
                .collectList()
                .doFinally(signalType -> socket.dispose());
    }

    /**
     * 测试Fire-and-Forget模式
     * POST /api/rsocket-test/fire-and-forget
     * Body: {"message": "Fire and Forget Message"}
     */
    @PostMapping("/fire-and-forget")
    public Mono<String> testFireAndForget(@RequestBody TestMessage request) {
        RSocket socket = getRSocketClient();

        return socket.fireAndForget(DefaultPayload.create(
                request.getMessage().getBytes(),
                "fire-and-forget".getBytes()
        ))
                .then(Mono.just("Fire-and-Forget message sent successfully"))
                .doFinally(signalType -> socket.dispose());
    }

    /**
     * 测试Channel模式
     * POST /api/rsocket-test/channel
     * Body: {"messages": ["Message 1", "Message 2", "Message 3"]}
     */
    @PostMapping("/channel")
    public Mono<List<String>> testChannel(@RequestBody ChannelTestMessage request) {
        RSocket socket = getRSocketClient();

        Flux<String> requestFlux = Flux.fromIterable(request.getMessages())
                .delayElements(Duration.ofMillis(500));

        return socket.requestChannel(
                requestFlux.map(msg -> DefaultPayload.create(
                        msg.getBytes(),
                        "channel".getBytes()
                ))
        )
                .map(payload -> payload.getDataUtf8())
                .collectList()
                .doFinally(signalType -> socket.dispose());
    }

    /**
     * 获取RSocket服务状态
     * GET /api/rsocket-test/status
     */
    @GetMapping("/status")
    public Mono<String> getRSocketStatus() {
        try {
            RSocket socket = getRSocketClient();
            socket.dispose();
            return Mono.just("RSocket服务运行正常，端口：7000");
        } catch (Exception e) {
            return Mono.just("RSocket服务连接失败：" + e.getMessage());
        }
    }

    // 请求消息类
    public static class TestMessage {
        private String message;

        public TestMessage() {}

        public TestMessage(String message) {
            this.message = message;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    // 流测试消息类
    public static class StreamTestMessage {
        private String message;
        private Integer count;

        public StreamTestMessage() {}

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }
    }

    // 通道测试消息类
    public static class ChannelTestMessage {
        private List<String> messages;

        public ChannelTestMessage() {}

        public List<String> getMessages() {
            return messages;
        }

        public void setMessages(List<String> messages) {
            this.messages = messages;
        }
    }
}
