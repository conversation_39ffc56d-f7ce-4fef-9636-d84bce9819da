package com.ijlynivfhp.wyj202506.util;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;

/**
 * 数据类型转换工具类
 * 提供基础数据类型的null安全转换方法
 */
public class DataUtils {

    /**
     * Integer转换，null时返回0
     */
    public static int toInt(Integer value) {
        return value != null ? value : 0;
    }

    /**
     * Integer转换，null时返回指定默认值
     */
    public static int toInt(Integer value, int defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * Long转换，null时返回0L
     */
    public static long toLong(Long value) {
        return value != null ? value : 0L;
    }

    /**
     * Long转换，null时返回指定默认值
     */
    public static long toLong(Long value, long defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * Double转换，null时返回0.0
     */
    public static double toDouble(Double value) {
        return value != null ? value : 0.0;
    }

    /**
     * Double转换，null时返回指定默认值
     */
    public static double toDouble(Double value, double defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * Float转换，null时返回0.0f
     */
    public static float toFloat(Float value) {
        return value != null ? value : 0.0f;
    }

    /**
     * Float转换，null时返回指定默认值
     */
    public static float toFloat(Float value, float defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * Boolean转换，null时返回false
     */
    public static boolean toBoolean(Boolean value) {
        return value != null ? value : false;
    }

    /**
     * Boolean转换，null时返回指定默认值
     */
    public static boolean toBoolean(Boolean value, boolean defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * String转换，null时返回空字符串
     */
    public static String toString(String value) {
        return value != null ? value : "";
    }

    /**
     * String转换，null时返回指定默认值
     */
    public static String toString(String value, String defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * BigDecimal转换，null时返回0
     */
    public static BigDecimal toBigDecimal(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }

    /**
     * BigDecimal转换，null时返回指定默认值
     */
    public static BigDecimal toBigDecimal(BigDecimal value, BigDecimal defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * 字符串转Integer，转换失败时返回0
     */
    public static int parseIntSafe(String value) {
        return parseIntSafe(value, 0);
    }

    /**
     * 字符串转Integer，转换失败时返回默认值
     */
    public static int parseIntSafe(String value, int defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 字符串转Long，转换失败时返回0L
     */
    public static long parseLongSafe(String value) {
        return parseLongSafe(value, 0L);
    }

    /**
     * 字符串转Long，转换失败时返回默认值
     */
    public static long parseLongSafe(String value, long defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Long.parseLong(value.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 字符串转Double，转换失败时返回0.0
     */
    public static double parseDoubleSafe(String value) {
        return parseDoubleSafe(value, 0.0);
    }

    /**
     * 字符串转Double，转换失败时返回默认值
     */
    public static double parseDoubleSafe(String value, double defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Double.parseDouble(value.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 检查集合是否为空
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 检查集合是否不为空
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    /**
     * 检查Map是否为空
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }

    /**
     * 检查Map是否不为空
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }

    /**
     * 检查数组是否为空
     */
    public static boolean isEmpty(Object[] array) {
        return array == null || array.length == 0;
    }

    /**
     * 检查数组是否不为空
     */
    public static boolean isNotEmpty(Object[] array) {
        return !isEmpty(array);
    }

    /**
     * 获取集合大小，null时返回0
     */
    public static int size(Collection<?> collection) {
        return collection != null ? collection.size() : 0;
    }

    /**
     * 获取Map大小，null时返回0
     */
    public static int size(Map<?, ?> map) {
        return map != null ? map.size() : 0;
    }

    /**
     * 获取数组长度，null时返回0
     */
    public static int length(Object[] array) {
        return array != null ? array.length : 0;
    }

    /**
     * 比较两个对象是否相等（null安全）
     */
    public static boolean equals(Object obj1, Object obj2) {
        if (obj1 == obj2) {
            return true;
        }
        if (obj1 == null || obj2 == null) {
            return false;
        }
        return obj1.equals(obj2);
    }

    /**
     * 获取对象的hashCode（null安全）
     */
    public static int hashCode(Object obj) {
        return obj != null ? obj.hashCode() : 0;
    }

    /**
     * 获取对象的字符串表示（null安全）
     */
    public static String safeToString(Object obj) {
        return obj != null ? obj.toString() : "null";
    }
}
