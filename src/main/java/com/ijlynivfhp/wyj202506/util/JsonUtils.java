package com.ijlynivfhp.wyj202506.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * 基于Jackson提供JSON序列化和反序列化功能
 */
@Slf4j
public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        // 配置ObjectMapper
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        OBJECT_MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        
        // 支持Java 8时间类型
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
    }

    /**
     * 获取ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

    /**
     * 对象转JSON字符串
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转JSON失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 对象转格式化的JSON字符串
     */
    public static String toPrettyJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("对象转格式化JSON失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转对象
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json) || clazz == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON转对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转对象（支持泛型）
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (StringUtils.isEmpty(json) || typeReference == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            log.error("JSON转对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转List
     */
    public static <T> List<T> fromJsonToList(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json) || clazz == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, 
                OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            log.error("JSON转List失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转Map
     */
    public static Map<String, Object> fromJsonToMap(String json) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.error("JSON转Map失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * JSON字符串转指定类型的Map
     */
    public static <K, V> Map<K, V> fromJsonToMap(String json, Class<K> keyClass, Class<V> valueClass) {
        if (StringUtils.isEmpty(json) || keyClass == null || valueClass == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(json, 
                OBJECT_MAPPER.getTypeFactory().constructMapType(Map.class, keyClass, valueClass));
        } catch (JsonProcessingException e) {
            log.error("JSON转Map失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 对象转Map
     */
    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(obj, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            log.error("对象转Map失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Map转对象
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        if (map == null || clazz == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(map, clazz);
        } catch (Exception e) {
            log.error("Map转对象失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 对象深拷贝
     */
    public static <T> T deepCopy(T obj, Class<T> clazz) {
        if (obj == null || clazz == null) {
            return null;
        }
        try {
            String json = toJson(obj);
            return fromJson(json, clazz);
        } catch (Exception e) {
            log.error("对象深拷贝失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查字符串是否为有效的JSON
     */
    public static boolean isValidJson(String json) {
        if (StringUtils.isEmpty(json)) {
            return false;
        }
        try {
            OBJECT_MAPPER.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为有效的JSON对象
     */
    public static boolean isValidJsonObject(String json) {
        if (StringUtils.isEmpty(json)) {
            return false;
        }
        try {
            return OBJECT_MAPPER.readTree(json).isObject();
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为有效的JSON数组
     */
    public static boolean isValidJsonArray(String json) {
        if (StringUtils.isEmpty(json)) {
            return false;
        }
        try {
            return OBJECT_MAPPER.readTree(json).isArray();
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    /**
     * 合并两个JSON对象
     */
    public static String mergeJson(String json1, String json2) {
        if (StringUtils.isEmpty(json1)) {
            return json2;
        }
        if (StringUtils.isEmpty(json2)) {
            return json1;
        }
        try {
            Map<String, Object> map1 = fromJsonToMap(json1);
            Map<String, Object> map2 = fromJsonToMap(json2);
            if (map1 != null && map2 != null) {
                map1.putAll(map2);
                return toJson(map1);
            }
        } catch (Exception e) {
            log.error("JSON合并失败: {}", e.getMessage(), e);
        }
        return json1;
    }

    /**
     * 从JSON中提取指定字段的值
     */
    public static Object getValueFromJson(String json, String fieldPath) {
        if (StringUtils.isEmpty(json) || StringUtils.isEmpty(fieldPath)) {
            return null;
        }
        try {
            String[] paths = fieldPath.split("\\.");
            Object current = fromJsonToMap(json);
            
            for (String path : paths) {
                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(path);
                } else {
                    return null;
                }
            }
            return current;
        } catch (Exception e) {
            log.error("从JSON提取字段值失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 格式化JSON字符串
     */
    public static String formatJson(String json) {
        if (StringUtils.isEmpty(json)) {
            return json;
        }
        try {
            Object obj = OBJECT_MAPPER.readValue(json, Object.class);
            return toPrettyJson(obj);
        } catch (JsonProcessingException e) {
            log.error("JSON格式化失败: {}", e.getMessage(), e);
            return json;
        }
    }

    /**
     * 压缩JSON字符串（移除空格和换行）
     */
    public static String compactJson(String json) {
        if (StringUtils.isEmpty(json)) {
            return json;
        }
        try {
            Object obj = OBJECT_MAPPER.readValue(json, Object.class);
            return toJson(obj);
        } catch (JsonProcessingException e) {
            log.error("JSON压缩失败: {}", e.getMessage(), e);
            return json;
        }
    }
}
