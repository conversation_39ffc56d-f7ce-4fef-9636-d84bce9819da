package com.ijlynivfhp.wyj202506.util;

import java.util.regex.Pattern;

/**
 * 验证工具类
 * 提供常用的数据验证方法
 */
public class ValidationUtils {

    // 正则表达式模式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{3,20}$");
    
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$");
    
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    
    private static final Pattern IP_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$"
    );
    
    private static final Pattern URL_PATTERN = Pattern.compile(
            "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$"
    );

    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * 验证手机号格式（中国大陆）
     */
    public static boolean isValidPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return false;
        }
        return PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 验证用户名格式（3-20位字母、数字、下划线）
     */
    public static boolean isValidUsername(String username) {
        if (StringUtils.isEmpty(username)) {
            return false;
        }
        return USERNAME_PATTERN.matcher(username).matches();
    }

    /**
     * 验证密码强度（至少8位，包含大小写字母和数字）
     */
    public static boolean isValidPassword(String password) {
        if (StringUtils.isEmpty(password)) {
            return false;
        }
        return PASSWORD_PATTERN.matcher(password).matches();
    }

    /**
     * 验证身份证号码格式（18位）
     */
    public static boolean isValidIdCard(String idCard) {
        if (StringUtils.isEmpty(idCard)) {
            return false;
        }
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            return false;
        }
        // 验证校验位
        return validateIdCardChecksum(idCard);
    }

    /**
     * 验证IP地址格式
     */
    public static boolean isValidIP(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }
        return IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 验证URL格式
     */
    public static boolean isValidURL(String url) {
        if (StringUtils.isEmpty(url)) {
            return false;
        }
        return URL_PATTERN.matcher(url).matches();
    }

    /**
     * 验证数字范围
     */
    public static boolean isInRange(int value, int min, int max) {
        return value >= min && value <= max;
    }

    /**
     * 验证数字范围
     */
    public static boolean isInRange(long value, long min, long max) {
        return value >= min && value <= max;
    }

    /**
     * 验证数字范围
     */
    public static boolean isInRange(double value, double min, double max) {
        return value >= min && value <= max;
    }

    /**
     * 验证字符串长度范围
     */
    public static boolean isLengthInRange(String str, int minLength, int maxLength) {
        if (str == null) {
            return minLength <= 0;
        }
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }

    /**
     * 验证是否为正整数
     */
    public static boolean isPositiveInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        try {
            int value = Integer.parseInt(str);
            return value > 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证是否为非负整数
     */
    public static boolean isNonNegativeInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        try {
            int value = Integer.parseInt(str);
            return value >= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证是否为数字
     */
    public static boolean isNumeric(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证是否为整数
     */
    public static boolean isInteger(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证是否为长整数
     */
    public static boolean isLong(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        try {
            Long.parseLong(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证是否为浮点数
     */
    public static boolean isDouble(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证是否为布尔值
     */
    public static boolean isBoolean(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return "true".equalsIgnoreCase(str) || "false".equalsIgnoreCase(str);
    }

    /**
     * 验证是否只包含字母
     */
    public static boolean isAlpha(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^[a-zA-Z]+$");
    }

    /**
     * 验证是否只包含字母和数字
     */
    public static boolean isAlphanumeric(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^[a-zA-Z0-9]+$");
    }

    /**
     * 验证是否只包含中文字符
     */
    public static boolean isChinese(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        return str.matches("^[\\u4e00-\\u9fa5]+$");
    }

    /**
     * 验证银行卡号格式（简单验证）
     */
    public static boolean isValidBankCard(String bankCard) {
        if (StringUtils.isEmpty(bankCard)) {
            return false;
        }
        // 银行卡号通常为16-19位数字
        return bankCard.matches("^\\d{16,19}$");
    }

    /**
     * 验证QQ号格式
     */
    public static boolean isValidQQ(String qq) {
        if (StringUtils.isEmpty(qq)) {
            return false;
        }
        // QQ号为5-11位数字，不能以0开头
        return qq.matches("^[1-9]\\d{4,10}$");
    }

    /**
     * 验证微信号格式
     */
    public static boolean isValidWeChat(String wechat) {
        if (StringUtils.isEmpty(wechat)) {
            return false;
        }
        // 微信号为6-20位，字母开头，可包含字母、数字、下划线、减号
        return wechat.matches("^[a-zA-Z][a-zA-Z0-9_-]{5,19}$");
    }

    /**
     * 验证邮政编码格式（中国）
     */
    public static boolean isValidPostalCode(String postalCode) {
        if (StringUtils.isEmpty(postalCode)) {
            return false;
        }
        return postalCode.matches("^\\d{6}$");
    }

    /**
     * 验证车牌号格式（中国）
     */
    public static boolean isValidLicensePlate(String licensePlate) {
        if (StringUtils.isEmpty(licensePlate)) {
            return false;
        }
        // 普通车牌：省份简称+字母+5位数字或字母
        String normalPattern = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{5}$";
        // 新能源车牌：省份简称+字母+6位数字或字母
        String newEnergyPattern = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-Z0-9]{6}$";
        
        return licensePlate.matches(normalPattern) || licensePlate.matches(newEnergyPattern);
    }

    /**
     * 身份证校验位验证
     */
    private static boolean validateIdCardChecksum(String idCard) {
        if (idCard.length() != 18) {
            return false;
        }
        
        // 权重因子
        int[] weights = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
        // 校验码
        char[] checkCodes = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
        
        int sum = 0;
        for (int i = 0; i < 17; i++) {
            char c = idCard.charAt(i);
            if (!Character.isDigit(c)) {
                return false;
            }
            sum += (c - '0') * weights[i];
        }
        
        int checkIndex = sum % 11;
        char expectedCheck = checkCodes[checkIndex];
        char actualCheck = idCard.charAt(17);
        
        return expectedCheck == actualCheck || (expectedCheck == 'X' && actualCheck == 'x');
    }

    /**
     * 验证日期格式（yyyy-MM-dd）
     */
    public static boolean isValidDate(String date) {
        if (StringUtils.isEmpty(date)) {
            return false;
        }
        return DateUtils.parseDate(date) != null;
    }

    /**
     * 验证时间格式（HH:mm:ss）
     */
    public static boolean isValidTime(String time) {
        if (StringUtils.isEmpty(time)) {
            return false;
        }
        return DateUtils.parseTime(time) != null;
    }

    /**
     * 验证日期时间格式（yyyy-MM-dd HH:mm:ss）
     */
    public static boolean isValidDateTime(String dateTime) {
        if (StringUtils.isEmpty(dateTime)) {
            return false;
        }
        return DateUtils.parseDateTime(dateTime) != null;
    }
}
