package com.ijlynivfhp.wyj202506.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * Spring容器工具类
 * 用于在非Spring管理的类中获取Spring Bean
 */
@Component
public class SpringContextUtils implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        SpringContextUtils.applicationContext = context;
    }

    /**
     * 获取ApplicationContext
     */
    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 根据Bean名称获取Bean
     */
    public static Object getBean(String beanName) {
        checkApplicationContext();
        return applicationContext.getBean(beanName);
    }

    /**
     * 根据Bean类型获取Bean
     */
    public static <T> T getBean(Class<T> beanClass) {
        checkApplicationContext();
        return applicationContext.getBean(beanClass);
    }

    /**
     * 根据Bean名称和类型获取Bean
     */
    public static <T> T getBean(String beanName, Class<T> beanClass) {
        checkApplicationContext();
        return applicationContext.getBean(beanName, beanClass);
    }

    /**
     * 检查是否包含指定名称的Bean
     */
    public static boolean containsBean(String beanName) {
        checkApplicationContext();
        return applicationContext.containsBean(beanName);
    }

    /**
     * 检查指定名称的Bean是否为单例
     */
    public static boolean isSingleton(String beanName) {
        checkApplicationContext();
        return applicationContext.isSingleton(beanName);
    }

    /**
     * 获取指定Bean的类型
     */
    public static Class<?> getType(String beanName) {
        checkApplicationContext();
        return applicationContext.getType(beanName);
    }

    /**
     * 获取指定Bean的别名
     */
    public static String[] getAliases(String beanName) {
        checkApplicationContext();
        return applicationContext.getAliases(beanName);
    }

    /**
     * 获取Environment
     */
    public static Environment getEnvironment() {
        checkApplicationContext();
        return applicationContext.getEnvironment();
    }

    /**
     * 获取配置属性值
     */
    public static String getProperty(String key) {
        checkApplicationContext();
        return applicationContext.getEnvironment().getProperty(key);
    }

    /**
     * 获取配置属性值，如果不存在则返回默认值
     */
    public static String getProperty(String key, String defaultValue) {
        checkApplicationContext();
        return applicationContext.getEnvironment().getProperty(key, defaultValue);
    }

    /**
     * 获取配置属性值并转换为指定类型
     */
    public static <T> T getProperty(String key, Class<T> targetType) {
        checkApplicationContext();
        return applicationContext.getEnvironment().getProperty(key, targetType);
    }

    /**
     * 获取配置属性值并转换为指定类型，如果不存在则返回默认值
     */
    public static <T> T getProperty(String key, Class<T> targetType, T defaultValue) {
        checkApplicationContext();
        return applicationContext.getEnvironment().getProperty(key, targetType, defaultValue);
    }

    /**
     * 检查是否包含指定的配置属性
     */
    public static boolean containsProperty(String key) {
        checkApplicationContext();
        return applicationContext.getEnvironment().containsProperty(key);
    }

    /**
     * 获取当前激活的Profile
     */
    public static String[] getActiveProfiles() {
        checkApplicationContext();
        return applicationContext.getEnvironment().getActiveProfiles();
    }

    /**
     * 获取默认的Profile
     */
    public static String[] getDefaultProfiles() {
        checkApplicationContext();
        return applicationContext.getEnvironment().getDefaultProfiles();
    }

    /**
     * 检查指定的Profile是否激活
     */
    public static boolean isProfileActive(String profile) {
        checkApplicationContext();
        String[] activeProfiles = getActiveProfiles();
        for (String activeProfile : activeProfiles) {
            if (activeProfile.equals(profile)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为开发环境
     */
    public static boolean isDevelopment() {
        return isProfileActive("dev") || isProfileActive("development");
    }

    /**
     * 检查是否为测试环境
     */
    public static boolean isTest() {
        return isProfileActive("test");
    }

    /**
     * 检查是否为生产环境
     */
    public static boolean isProduction() {
        return isProfileActive("prod") || isProfileActive("production");
    }

    /**
     * 发布事件
     */
    public static void publishEvent(Object event) {
        checkApplicationContext();
        applicationContext.publishEvent(event);
    }

    /**
     * 检查ApplicationContext是否已初始化
     */
    private static void checkApplicationContext() {
        if (applicationContext == null) {
            throw new IllegalStateException("ApplicationContext未初始化，请确保SpringContextUtils已被Spring容器管理");
        }
    }

    /**
     * 清理ApplicationContext（主要用于测试）
     */
    public static void clearApplicationContext() {
        applicationContext = null;
    }
}
