package com.ijlynivfhp.wyj202506.util;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.UUID;

/**
 * 加密工具类
 * 提供常用的加密、哈希和编码功能
 */
@Slf4j
public class CryptoUtils {

    private static final String MD5_ALGORITHM = "MD5";
    private static final String SHA1_ALGORITHM = "SHA-1";
    private static final String SHA256_ALGORITHM = "SHA-256";
    private static final String SHA512_ALGORITHM = "SHA-512";

    /**
     * MD5加密
     */
    public static String md5(String input) {
        return hash(input, MD5_ALGORITHM);
    }

    /**
     * SHA-1加密
     */
    public static String sha1(String input) {
        return hash(input, SHA1_ALGORITHM);
    }

    /**
     * SHA-256加密
     */
    public static String sha256(String input) {
        return hash(input, SHA256_ALGORITHM);
    }

    /**
     * SHA-512加密
     */
    public static String sha512(String input) {
        return hash(input, SHA512_ALGORITHM);
    }

    /**
     * 通用哈希方法
     */
    private static String hash(String input, String algorithm) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            log.error("哈希算法不支持: {}", algorithm, e);
            return null;
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * Base64编码
     */
    public static String base64Encode(String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        return Base64.getEncoder().encodeToString(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Base64编码（字节数组）
     */
    public static String base64Encode(byte[] input) {
        if (input == null || input.length == 0) {
            return null;
        }
        return Base64.getEncoder().encodeToString(input);
    }

    /**
     * Base64解码
     */
    public static String base64Decode(String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(input);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (IllegalArgumentException e) {
            log.error("Base64解码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Base64解码（返回字节数组）
     */
    public static byte[] base64DecodeToBytes(String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        try {
            return Base64.getDecoder().decode(input);
        } catch (IllegalArgumentException e) {
            log.error("Base64解码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * URL安全的Base64编码
     */
    public static String base64UrlEncode(String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        return Base64.getUrlEncoder().withoutPadding()
                .encodeToString(input.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * URL安全的Base64解码
     */
    public static String base64UrlDecode(String input) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        try {
            byte[] decodedBytes = Base64.getUrlDecoder().decode(input);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (IllegalArgumentException e) {
            log.error("Base64 URL解码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成随机盐值
     */
    public static String generateSalt() {
        return generateSalt(16);
    }

    /**
     * 生成指定长度的随机盐值
     */
    public static String generateSalt(int length) {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[length];
        random.nextBytes(salt);
        return bytesToHex(salt);
    }

    /**
     * 带盐值的SHA-256加密
     */
    public static String sha256WithSalt(String input, String salt) {
        if (StringUtils.isEmpty(input)) {
            return null;
        }
        String saltedInput = input + (salt != null ? salt : "");
        return sha256(saltedInput);
    }

    /**
     * 生成UUID
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成不带横线的UUID
     */
    public static String generateUUIDWithoutHyphens() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成随机字符串（包含字母和数字）
     */
    public static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 生成随机数字字符串
     */
    public static String generateRandomNumeric(int length) {
        String chars = "0123456789";
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        return sb.toString();
    }

    /**
     * 简单的密码强度检查
     */
    public static boolean isStrongPassword(String password) {
        if (StringUtils.isEmpty(password) || password.length() < 8) {
            return false;
        }
        
        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasDigit = false;
        boolean hasSpecial = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (!Character.isLetterOrDigit(c)) {
                hasSpecial = true;
            }
        }
        
        // 至少包含大写字母、小写字母、数字中的三种
        int count = 0;
        if (hasUpper) count++;
        if (hasLower) count++;
        if (hasDigit) count++;
        if (hasSpecial) count++;
        
        return count >= 3;
    }

    /**
     * 验证哈希值
     */
    public static boolean verifyHash(String input, String hash, String algorithm) {
        if (StringUtils.isEmpty(input) || StringUtils.isEmpty(hash)) {
            return false;
        }
        String computedHash = hash(input, algorithm);
        return hash.equalsIgnoreCase(computedHash);
    }

    /**
     * 验证MD5哈希值
     */
    public static boolean verifyMd5(String input, String hash) {
        return verifyHash(input, hash, MD5_ALGORITHM);
    }

    /**
     * 验证SHA-256哈希值
     */
    public static boolean verifySha256(String input, String hash) {
        return verifyHash(input, hash, SHA256_ALGORITHM);
    }

    /**
     * 验证带盐值的SHA-256哈希值
     */
    public static boolean verifySha256WithSalt(String input, String salt, String hash) {
        if (StringUtils.isEmpty(input) || StringUtils.isEmpty(hash)) {
            return false;
        }
        String computedHash = sha256WithSalt(input, salt);
        return hash.equalsIgnoreCase(computedHash);
    }

    /**
     * 十六进制字符串转字节数组
     */
    public static byte[] hexToBytes(String hex) {
        if (StringUtils.isEmpty(hex) || hex.length() % 2 != 0) {
            return null;
        }
        byte[] bytes = new byte[hex.length() / 2];
        for (int i = 0; i < hex.length(); i += 2) {
            bytes[i / 2] = (byte) Integer.parseInt(hex.substring(i, i + 2), 16);
        }
        return bytes;
    }

    /**
     * 生成安全的随机数
     */
    public static int generateSecureRandomInt(int bound) {
        SecureRandom random = new SecureRandom();
        return random.nextInt(bound);
    }

    /**
     * 生成安全的随机长整数
     */
    public static long generateSecureRandomLong() {
        SecureRandom random = new SecureRandom();
        return random.nextLong();
    }
}
