package com.ijlynivfhp.wyj202506.repository;

import com.ijlynivfhp.wyj202506.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户Repository接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 根据用户名查找用户（未删除）
     */
    Optional<User> findByUsernameAndDeleted(String username, Integer deleted);

    /**
     * 根据邮箱查找用户（未删除）
     */
    Optional<User> findByEmailAndDeleted(String email, Integer deleted);

    /**
     * 根据手机号查找用户（未删除）
     */
    Optional<User> findByPhoneAndDeleted(String phone, Integer deleted);

    /**
     * 检查用户名是否存在（未删除）
     */
    boolean existsByUsernameAndDeleted(String username, Integer deleted);

    /**
     * 检查邮箱是否存在（未删除）
     */
    boolean existsByEmailAndDeleted(String email, Integer deleted);

    /**
     * 检查手机号是否存在（未删除）
     */
    boolean existsByPhoneAndDeleted(String phone, Integer deleted);

    /**
     * 根据状态查找用户（未删除）
     */
    List<User> findByStatusAndDeleted(Integer status, Integer deleted);

    /**
     * 根据状态分页查找用户（未删除）
     */
    Page<User> findByStatusAndDeleted(Integer status, Integer deleted, Pageable pageable);

    /**
     * 查找所有未删除的用户
     */
    List<User> findByDeleted(Integer deleted);

    /**
     * 分页查找所有未删除的用户
     */
    Page<User> findByDeleted(Integer deleted, Pageable pageable);

    /**
     * 根据创建时间范围查找用户（未删除）
     */
    @Query("SELECT u FROM User u WHERE u.deleted = :deleted " +
           "AND u.createTime >= :startTime AND u.createTime <= :endTime")
    List<User> findByCreateTimeBetweenAndDeleted(
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("deleted") Integer deleted);

    /**
     * 根据用户名模糊查询（未删除）
     */
    @Query("SELECT u FROM User u WHERE u.deleted = :deleted " +
           "AND u.username LIKE CONCAT('%', :username, '%')")
    List<User> findByUsernameLikeAndDeleted(@Param("username") String username, @Param("deleted") Integer deleted);

    /**
     * 软删除用户
     */
    @Modifying
    @Query("UPDATE User u SET u.deleted = 1, u.updateTime = :updateTime, u.updateBy = :updateBy WHERE u.id = :id")
    int softDeleteById(@Param("id") Long id, @Param("updateTime") LocalDateTime updateTime, @Param("updateBy") String updateBy);

    /**
     * 批量软删除用户
     */
    @Modifying
    @Query("UPDATE User u SET u.deleted = 1, u.updateTime = :updateTime, u.updateBy = :updateBy WHERE u.id IN :ids")
    int softDeleteByIds(@Param("ids") List<Long> ids, @Param("updateTime") LocalDateTime updateTime, @Param("updateBy") String updateBy);

    /**
     * 更新最后登录时间
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginTime = :lastLoginTime, u.updateTime = :updateTime WHERE u.id = :id")
    int updateLastLoginTime(@Param("id") Long id, @Param("lastLoginTime") LocalDateTime lastLoginTime, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 统计未删除用户数量
     */
    long countByDeleted(Integer deleted);

    /**
     * 根据状态统计未删除用户数量
     */
    long countByStatusAndDeleted(Integer status, Integer deleted);
}
