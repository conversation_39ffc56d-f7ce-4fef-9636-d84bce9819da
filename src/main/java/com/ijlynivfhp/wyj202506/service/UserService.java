package com.ijlynivfhp.wyj202506.service;

import com.ijlynivfhp.wyj202506.common.PageResult;
import com.ijlynivfhp.wyj202506.dto.*;
import com.ijlynivfhp.wyj202506.entity.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 创建用户
     */
    UserDTO createUser(UserCreateRequest request);

    /**
     * 根据ID获取用户
     */
    UserDTO getUserById(Long id);

    /**
     * 更新用户
     */
    UserDTO updateUser(Long id, UserUpdateRequest request);

    /**
     * 删除用户（软删除）
     */
    void deleteUser(Long id);

    /**
     * 批量删除用户（软删除）
     */
    void deleteUsers(List<Long> ids);

    /**
     * 分页查询用户
     */
    PageResult<UserDTO> queryUsers(UserQueryRequest request);

    /**
     * 获取所有用户
     */
    List<UserDTO> getAllUsers();

    /**
     * 启用用户
     */
    void enableUser(Long id);

    /**
     * 禁用用户
     */
    void disableUser(Long id);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);



    /**
     * 更新最后登录时间
     */
    void updateLastLoginTime(Long id);

    /**
     * 修改密码
     */
    void changePassword(Long id, String oldPassword, String newPassword);

    /**
     * 重置密码
     */
    void resetPassword(Long id, String newPassword);

    /**
     * 验证密码
     */
    boolean validatePassword(String username, String password);

    /**
     * 统计用户数量
     */
    long countUsers();



    /**
     * 实体转DTO
     */
    UserDTO convertToDTO(User user);

    /**
     * 创建请求转实体
     */
    User convertToEntity(UserCreateRequest request);

    /**
     * 更新请求应用到实体
     */
    void applyUpdateToEntity(User user, UserUpdateRequest request);
}
