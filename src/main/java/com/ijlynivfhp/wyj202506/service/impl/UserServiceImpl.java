package com.ijlynivfhp.wyj202506.service.impl;

import com.ijlynivfhp.wyj202506.common.PageResult;
import com.ijlynivfhp.wyj202506.dto.*;
import com.ijlynivfhp.wyj202506.entity.User;
import com.ijlynivfhp.wyj202506.repository.UserRepository;
import com.ijlynivfhp.wyj202506.service.UserService;
import javax.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;

    @Override
    @Transactional
    public UserDTO createUser(UserCreateRequest request) {
        log.info("创建用户: {}", request.getUsername());
        
        // 检查用户名是否已存在
        if (existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在: " + request.getUsername());
        }
        
        // 检查邮箱是否已存在
        if (StringUtils.hasText(request.getEmail()) && userRepository.existsByEmailAndDeleted(request.getEmail(), 0)) {
            throw new RuntimeException("邮箱已存在: " + request.getEmail());
        }

        // 检查手机号是否已存在
        if (StringUtils.hasText(request.getPhone()) && userRepository.existsByPhoneAndDeleted(request.getPhone(), 0)) {
            throw new RuntimeException("手机号已存在: " + request.getPhone());
        }
        
        User user = convertToEntity(request);
        user.setCreateBy("system"); // TODO: 从当前登录用户获取
        user.setUpdateBy("system");
        
        User savedUser = userRepository.save(user);
        log.info("用户创建成功: {}", savedUser.getId());
        
        return convertToDTO(savedUser);
    }

    @Override
    public UserDTO getUserById(Long id) {
        log.debug("根据ID获取用户: {}", id);
        
        Optional<User> userOpt = userRepository.findById(id);
        if (!userOpt.isPresent() || userOpt.get().isDeleted()) {
            throw new RuntimeException("用户不存在: " + id);
        }
        
        return convertToDTO(userOpt.get());
    }



    @Override
    @Transactional
    public UserDTO updateUser(Long id, UserUpdateRequest request) {
        log.info("更新用户: {}", id);
        
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + id));
        
        if (user.isDeleted()) {
            throw new RuntimeException("用户已被删除: " + id);
        }
        
        // 检查邮箱是否已被其他用户使用
        if (StringUtils.hasText(request.getEmail()) && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmailAndDeleted(request.getEmail(), 0)) {
                throw new RuntimeException("邮箱已存在: " + request.getEmail());
            }
        }

        // 检查手机号是否已被其他用户使用
        if (StringUtils.hasText(request.getPhone()) && !request.getPhone().equals(user.getPhone())) {
            if (userRepository.existsByPhoneAndDeleted(request.getPhone(), 0)) {
                throw new RuntimeException("手机号已存在: " + request.getPhone());
            }
        }
        
        applyUpdateToEntity(user, request);
        user.setUpdateBy("system"); // TODO: 从当前登录用户获取
        
        User savedUser = userRepository.save(user);
        log.info("用户更新成功: {}", savedUser.getId());
        
        return convertToDTO(savedUser);
    }

    @Override
    @Transactional
    public void deleteUser(Long id) {
        log.info("删除用户: {}", id);
        
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + id));
        
        if (user.isDeleted()) {
            throw new RuntimeException("用户已被删除: " + id);
        }
        
        int updated = userRepository.softDeleteById(id, LocalDateTime.now(), "system");
        if (updated > 0) {
            log.info("用户删除成功: {}", id);
        } else {
            throw new RuntimeException("用户删除失败: " + id);
        }
    }

    @Override
    @Transactional
    public void deleteUsers(List<Long> ids) {
        log.info("批量删除用户: {}", ids);
        
        if (ids == null || ids.isEmpty()) {
            return;
        }
        
        int updated = userRepository.softDeleteByIds(ids, LocalDateTime.now(), "system");
        log.info("批量删除用户成功，删除数量: {}", updated);
    }

    @Override
    public PageResult<UserDTO> queryUsers(UserQueryRequest request) {
        log.debug("分页查询用户: {}", request);
        
        request.validateAndFix();
        
        // 构建查询条件
        Specification<User> spec = buildSpecification(request);
        
        // 构建分页和排序
        Sort sort = Sort.by(
                request.getSortOrder().equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC,
                request.getSortField()
        );
        Pageable pageable = PageRequest.of(request.getCurrent() - 1, request.getSize(), sort);
        
        // 执行查询
        Page<User> userPage = userRepository.findAll(spec, pageable);
        
        // 转换结果
        List<UserDTO> userDTOs = userPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        
        return new PageResult<>(userDTOs, userPage.getTotalElements(), 
                request.getCurrent(), request.getSize());
    }

    @Override
    public List<UserDTO> getAllUsers() {
        log.debug("获取所有用户");
        
        List<User> users = userRepository.findByDeleted(0);
        return users.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }



    @Override
    @Transactional
    public void enableUser(Long id) {
        log.info("启用用户: {}", id);
        updateUserStatus(id, User.Status.ENABLED.getCode());
    }

    @Override
    @Transactional
    public void disableUser(Long id) {
        log.info("禁用用户: {}", id);
        updateUserStatus(id, User.Status.DISABLED.getCode());
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsernameAndDeleted(username, 0);
    }



    @Override
    @Transactional
    public void updateLastLoginTime(Long id) {
        log.debug("更新最后登录时间: {}", id);
        
        int updated = userRepository.updateLastLoginTime(id, LocalDateTime.now(), LocalDateTime.now());
        if (updated == 0) {
            log.warn("更新最后登录时间失败，用户可能不存在: {}", id);
        }
    }

    @Override
    @Transactional
    public void changePassword(Long id, String oldPassword, String newPassword) {
        log.info("修改密码: {}", id);
        
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + id));
        
        if (user.isDeleted()) {
            throw new RuntimeException("用户已被删除: " + id);
        }
        
        // TODO: 验证旧密码
        // if (!passwordEncoder.matches(oldPassword, user.getEncryptedPassword())) {
        //     throw new RuntimeException("原密码错误");
        // }
        
        // TODO: 加密新密码
        // user.setEncryptedPassword(passwordEncoder.encode(newPassword));
        user.setEncryptedPassword(newPassword); // 临时直接设置，实际应该加密
        user.setUpdateBy("system");
        
        userRepository.save(user);
        log.info("密码修改成功: {}", id);
    }

    @Override
    @Transactional
    public void resetPassword(Long id, String newPassword) {
        log.info("重置密码: {}", id);
        
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + id));
        
        if (user.isDeleted()) {
            throw new RuntimeException("用户已被删除: " + id);
        }
        
        // TODO: 加密新密码
        // user.setEncryptedPassword(passwordEncoder.encode(newPassword));
        user.setEncryptedPassword(newPassword); // 临时直接设置，实际应该加密
        user.setUpdateBy("system");
        
        userRepository.save(user);
        log.info("密码重置成功: {}", id);
    }

    @Override
    public boolean validatePassword(String username, String password) {
        log.debug("验证密码: {}", username);
        
        Optional<User> userOpt = userRepository.findByUsernameAndDeleted(username, 0);
        if (!userOpt.isPresent()) {
            return false;
        }
        
        User user = userOpt.get();
        if (!user.isEnabled()) {
            return false;
        }
        
        // TODO: 使用密码编码器验证
        // return passwordEncoder.matches(password, user.getEncryptedPassword());
        return password.equals(user.getEncryptedPassword()); // 临时直接比较，实际应该使用加密验证
    }

    @Override
    public long countUsers() {
        return userRepository.countByDeleted(0);
    }



    @Override
    public UserDTO convertToDTO(User user) {
        if (user == null) {
            return null;
        }

        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setPhone(user.getPhone());
        dto.setRealName(user.getRealName());
        dto.setAvatar(user.getAvatar());
        dto.setStatus(user.getStatus());
        dto.setStatusDesc(User.Status.fromCode(user.getStatus()).getDescription());
        dto.setLastLoginTime(user.getLastLoginTime());
        dto.setCreateTime(user.getCreateTime());
        dto.setUpdateTime(user.getUpdateTime());
        dto.setCreateBy(user.getCreateBy());
        dto.setUpdateBy(user.getUpdateBy());
        dto.setRemark(user.getRemark());

        return dto;
    }

    @Override
    public User convertToEntity(UserCreateRequest request) {
        if (request == null) {
            return null;
        }

        User user = new User();
        user.setUsername(request.getUsername());
        // TODO: 加密密码
        // user.setEncryptedPassword(passwordEncoder.encode(request.getPassword()));
        user.setEncryptedPassword(request.getPassword()); // 临时直接设置，实际应该加密
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setRealName(request.getRealName());
        user.setAvatar(request.getAvatar());
        user.setStatus(request.getStatus());
        user.setRemark(request.getRemark());

        return user;
    }

    @Override
    public void applyUpdateToEntity(User user, UserUpdateRequest request) {
        if (user == null || request == null) {
            return;
        }

        if (StringUtils.hasText(request.getEmail())) {
            user.setEmail(request.getEmail());
        }
        if (StringUtils.hasText(request.getPhone())) {
            user.setPhone(request.getPhone());
        }
        if (StringUtils.hasText(request.getRealName())) {
            user.setRealName(request.getRealName());
        }
        if (StringUtils.hasText(request.getAvatar())) {
            user.setAvatar(request.getAvatar());
        }
        if (request.getStatus() != null) {
            user.setStatus(request.getStatus());
        }
        if (StringUtils.hasText(request.getRemark())) {
            user.setRemark(request.getRemark());
        }
    }

    /**
     * 更新用户状态
     */
    private void updateUserStatus(Long id, Integer status) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("用户不存在: " + id));

        if (user.isDeleted()) {
            throw new RuntimeException("用户已被删除: " + id);
        }

        user.setStatus(status);
        user.setUpdateBy("system"); // TODO: 从当前登录用户获取

        userRepository.save(user);
    }

    /**
     * 构建查询条件
     */
    private Specification<User> buildSpecification(UserQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 未删除
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));

            // 用户名模糊查询
            if (StringUtils.hasText(request.getUsername())) {
                predicates.add(criteriaBuilder.like(root.get("username"), "%" + request.getUsername() + "%"));
            }

            // 邮箱模糊查询
            if (StringUtils.hasText(request.getEmail())) {
                predicates.add(criteriaBuilder.like(root.get("email"), "%" + request.getEmail() + "%"));
            }

            // 手机号模糊查询
            if (StringUtils.hasText(request.getPhone())) {
                predicates.add(criteriaBuilder.like(root.get("phone"), "%" + request.getPhone() + "%"));
            }

            // 真实姓名模糊查询
            if (StringUtils.hasText(request.getRealName())) {
                predicates.add(criteriaBuilder.like(root.get("realName"), "%" + request.getRealName() + "%"));
            }

            // 状态精确查询
            if (request.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), request.getStatus()));
            }

            // 创建时间范围查询
            if (request.getCreateTimeStart() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), request.getCreateTimeStart()));
            }
            if (request.getCreateTimeEnd() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), request.getCreateTimeEnd()));
            }

            // 创建人查询
            if (StringUtils.hasText(request.getCreateBy())) {
                predicates.add(criteriaBuilder.equal(root.get("createBy"), request.getCreateBy()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
