package com.ijlynivfhp.wyj202506.service;

import com.ijlynivfhp.wyj202506.dto.UserCreateRequest;
import com.ijlynivfhp.wyj202506.dto.UserDTO;
import com.ijlynivfhp.wyj202506.dto.UserQueryRequest;
import com.ijlynivfhp.wyj202506.common.PageResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户服务测试类
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class UserServiceTest {

    @Autowired
    private UserService userService;

    @Test
    void testCreateUser() {
        // 准备测试数据
        UserCreateRequest request = new UserCreateRequest();
        request.setUsername("testuser");
        request.setPassword("test123");
        request.setEmail("<EMAIL>");
        request.setPhone("13800138000");
        request.setRealName("测试用户");
        request.setStatus(1);
        request.setRemark("测试创建用户");

        // 执行测试
        UserDTO result = userService.createUser(request);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals("testuser", result.getUsername());
        assertEquals("<EMAIL>", result.getEmail());
        assertEquals("13800138000", result.getPhone());
        assertEquals("测试用户", result.getRealName());
        assertEquals(1, result.getStatus());
        assertEquals("启用", result.getStatusDesc());
        assertEquals("测试创建用户", result.getRemark());
    }

    @Test
    void testGetUserById() {
        // 先创建一个用户
        UserCreateRequest request = new UserCreateRequest();
        request.setUsername("testuser2");
        request.setPassword("test123");
        request.setEmail("<EMAIL>");
        
        UserDTO created = userService.createUser(request);
        
        // 测试根据ID获取用户
        UserDTO result = userService.getUserById(created.getId());
        
        // 验证结果
        assertNotNull(result);
        assertEquals(created.getId(), result.getId());
        assertEquals("testuser2", result.getUsername());
        assertEquals("<EMAIL>", result.getEmail());
    }

    @Test
    void testQueryUsers() {
        // 创建测试数据
        for (int i = 1; i <= 5; i++) {
            UserCreateRequest request = new UserCreateRequest();
            request.setUsername("querytest" + i);
            request.setPassword("test123");
            request.setEmail("querytest" + i + "@example.com");
            request.setRealName("查询测试用户" + i);
            request.setStatus(i % 2); // 交替设置状态
            
            userService.createUser(request);
        }

        // 测试分页查询
        UserQueryRequest queryRequest = new UserQueryRequest();
        queryRequest.setCurrent(1);
        queryRequest.setSize(3);
        queryRequest.setUsername("querytest");

        PageResult<UserDTO> result = userService.queryUsers(queryRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getTotal() >= 5);
        assertEquals(3, result.getSize());
        assertEquals(1, result.getCurrent());
        assertNotNull(result.getRecords());
        assertTrue(result.getRecords().size() <= 3);
    }

    @Test
    void testExistsByUsername() {
        // 创建测试用户
        UserCreateRequest request = new UserCreateRequest();
        request.setUsername("existstest");
        request.setPassword("test123");
        request.setEmail("<EMAIL>");
        
        userService.createUser(request);

        // 测试用户名存在性检查
        assertTrue(userService.existsByUsername("existstest"));
        assertFalse(userService.existsByUsername("notexists"));
    }

    @Test
    void testDeleteUser() {
        // 创建测试用户
        UserCreateRequest request = new UserCreateRequest();
        request.setUsername("deletetest");
        request.setPassword("test123");
        request.setEmail("<EMAIL>");
        
        UserDTO created = userService.createUser(request);

        // 测试删除用户
        userService.deleteUser(created.getId());

        // 验证用户已被删除（应该抛出异常）
        assertThrows(RuntimeException.class, () -> {
            userService.getUserById(created.getId());
        });
    }

    @Test
    void testEnableAndDisableUser() {
        // 创建测试用户
        UserCreateRequest request = new UserCreateRequest();
        request.setUsername("statustest");
        request.setPassword("test123");
        request.setEmail("<EMAIL>");
        request.setStatus(1); // 初始状态为启用
        
        UserDTO created = userService.createUser(request);

        // 测试禁用用户
        userService.disableUser(created.getId());
        UserDTO disabled = userService.getUserById(created.getId());
        assertEquals(0, disabled.getStatus());
        assertEquals("禁用", disabled.getStatusDesc());

        // 测试启用用户
        userService.enableUser(created.getId());
        UserDTO enabled = userService.getUserById(created.getId());
        assertEquals(1, enabled.getStatus());
        assertEquals("启用", enabled.getStatusDesc());
    }
}
