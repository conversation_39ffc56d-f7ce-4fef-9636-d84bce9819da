package com.ijlynivfhp.wyj202506.util;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工具类测试示例
 */
@SpringBootTest
class UtilsTest {

    @Test
    void testDataUtils() {
        System.out.println("=== DataUtils 测试 ===");
        
        // 测试null转换
        Integer nullInt = null;
        Long nullLong = null;
        String nullStr = null;
        
        assertEquals(0, DataUtils.toInt(nullInt));
        assertEquals(100, DataUtils.toInt(nullInt, 100));
        assertEquals(0L, DataUtils.toLong(nullLong));
        assertEquals("", DataUtils.toString(nullStr));
        assertEquals("default", DataUtils.toString(nullStr, "default"));
        
        // 测试字符串解析
        assertEquals(123, DataUtils.parseIntSafe("123"));
        assertEquals(0, DataUtils.parseIntSafe("abc"));
        assertEquals(999, DataUtils.parseIntSafe("abc", 999));
        
        System.out.println("DataUtils测试通过");
    }

    @Test
    void testStringUtils() {
        System.out.println("=== StringUtils 测试 ===");
        
        // 测试空值检查
        assertTrue(StringUtils.isEmpty(null));
        assertTrue(StringUtils.isEmpty(""));
        assertFalse(StringUtils.isEmpty("test"));
        
        assertTrue(StringUtils.isBlank(null));
        assertTrue(StringUtils.isBlank(""));
        assertTrue(StringUtils.isBlank("   "));
        assertFalse(StringUtils.isBlank("test"));
        
        // 测试字符串操作
        assertEquals("hello", StringUtils.defaultIfEmpty("", "hello"));
        assertEquals("test", StringUtils.defaultIfEmpty("test", "hello"));
        
        // 测试掩码
        assertEquals("138****1234", StringUtils.maskPhone("13812341234"));
        assertEquals("t***@example.com", StringUtils.maskEmail("<EMAIL>"));
        
        // 测试验证
        assertTrue(StringUtils.isValidEmail("<EMAIL>"));
        assertFalse(StringUtils.isValidEmail("invalid-email"));
        assertTrue(StringUtils.isValidPhone("13812345678"));
        assertFalse(StringUtils.isValidPhone("12345"));
        
        System.out.println("StringUtils测试通过");
    }

    @Test
    void testDateUtils() {
        System.out.println("=== DateUtils 测试 ===");
        
        // 测试当前时间
        LocalDateTime now = DateUtils.now();
        LocalDate today = DateUtils.today();
        assertNotNull(now);
        assertNotNull(today);
        
        // 测试格式化
        String dateStr = DateUtils.format(now);
        assertNotNull(dateStr);
        System.out.println("当前时间: " + dateStr);
        
        // 测试解析
        LocalDateTime parsed = DateUtils.parseDateTime("2023-12-25 10:30:00");
        assertNotNull(parsed);
        assertEquals(2023, parsed.getYear());
        assertEquals(12, parsed.getMonthValue());
        assertEquals(25, parsed.getDayOfMonth());
        
        // 测试日期计算
        LocalDate date1 = LocalDate.of(2023, 1, 1);
        LocalDate date2 = LocalDate.of(2023, 1, 10);
        assertEquals(9, DateUtils.daysBetween(date1, date2));
        
        System.out.println("DateUtils测试通过");
    }

    @Test
    void testJsonUtils() {
        System.out.println("=== JsonUtils 测试 ===");
        
        // 测试对象转JSON
        Map<String, Object> map = new HashMap<>();
        map.put("name", "张三");
        map.put("age", 25);
        map.put("active", true);
        
        String json = JsonUtils.toJson(map);
        assertNotNull(json);
        System.out.println("JSON: " + json);
        
        // 测试JSON转对象
        Map<String, Object> parsed = JsonUtils.fromJsonToMap(json);
        assertNotNull(parsed);
        assertEquals("张三", parsed.get("name"));
        assertEquals(25, ((Number) parsed.get("age")).intValue());
        assertEquals(true, parsed.get("active"));
        
        // 测试JSON验证
        assertTrue(JsonUtils.isValidJson(json));
        assertFalse(JsonUtils.isValidJson("invalid json"));
        
        System.out.println("JsonUtils测试通过");
    }

    @Test
    void testCryptoUtils() {
        System.out.println("=== CryptoUtils 测试 ===");
        
        String input = "Hello World";
        
        // 测试哈希
        String md5 = CryptoUtils.md5(input);
        String sha256 = CryptoUtils.sha256(input);
        assertNotNull(md5);
        assertNotNull(sha256);
        System.out.println("MD5: " + md5);
        System.out.println("SHA256: " + sha256);
        
        // 测试Base64
        String encoded = CryptoUtils.base64Encode(input);
        String decoded = CryptoUtils.base64Decode(encoded);
        assertNotNull(encoded);
        assertEquals(input, decoded);
        System.out.println("Base64编码: " + encoded);
        
        // 测试随机生成
        String uuid = CryptoUtils.generateUUID();
        String randomStr = CryptoUtils.generateRandomString(10);
        String salt = CryptoUtils.generateSalt();
        assertNotNull(uuid);
        assertNotNull(randomStr);
        assertNotNull(salt);
        assertEquals(10, randomStr.length());
        System.out.println("UUID: " + uuid);
        System.out.println("随机字符串: " + randomStr);
        System.out.println("盐值: " + salt);
        
        // 测试密码强度
        assertTrue(CryptoUtils.isStrongPassword("Password123!"));
        assertFalse(CryptoUtils.isStrongPassword("weak"));
        
        System.out.println("CryptoUtils测试通过");
    }

    @Test
    void testValidationUtils() {
        System.out.println("=== ValidationUtils 测试 ===");
        
        // 测试邮箱验证
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"));
        assertFalse(ValidationUtils.isValidEmail("invalid-email"));
        
        // 测试手机号验证
        assertTrue(ValidationUtils.isValidPhone("13812345678"));
        assertFalse(ValidationUtils.isValidPhone("12345"));
        
        // 测试用户名验证
        assertTrue(ValidationUtils.isValidUsername("user123"));
        assertFalse(ValidationUtils.isValidUsername("u"));
        
        // 测试数字验证
        assertTrue(ValidationUtils.isNumeric("123.45"));
        assertFalse(ValidationUtils.isNumeric("abc"));
        assertTrue(ValidationUtils.isInteger("123"));
        assertFalse(ValidationUtils.isInteger("123.45"));
        
        // 测试范围验证
        assertTrue(ValidationUtils.isInRange(50, 1, 100));
        assertFalse(ValidationUtils.isInRange(150, 1, 100));
        
        // 测试长度验证
        assertTrue(ValidationUtils.isLengthInRange("hello", 3, 10));
        assertFalse(ValidationUtils.isLengthInRange("hi", 3, 10));
        
        // 测试IP地址验证
        assertTrue(ValidationUtils.isValidIP("***********"));
        assertFalse(ValidationUtils.isValidIP("999.999.999.999"));
        
        // 测试URL验证
        assertTrue(ValidationUtils.isValidURL("https://www.example.com"));
        assertFalse(ValidationUtils.isValidURL("not-a-url"));
        
        System.out.println("ValidationUtils测试通过");
    }

    @Test
    void testUtilsIntegration() {
        System.out.println("=== 工具类集成测试 ===");
        
        // 模拟用户注册场景
        String username = "testuser";
        String password = "TestPass123!";
        String email = "<EMAIL>";
        String phone = "13812345678";
        
        // 验证输入
        assertTrue(ValidationUtils.isValidUsername(username));
        assertTrue(CryptoUtils.isStrongPassword(password));
        assertTrue(ValidationUtils.isValidEmail(email));
        assertTrue(ValidationUtils.isValidPhone(phone));
        
        // 加密密码
        String salt = CryptoUtils.generateSalt();
        String hashedPassword = CryptoUtils.sha256WithSalt(password, salt);
        assertNotNull(hashedPassword);
        
        // 创建用户数据
        Map<String, Object> userData = new HashMap<>();
        userData.put("username", username);
        userData.put("password", hashedPassword);
        userData.put("salt", salt);
        userData.put("email", email);
        userData.put("phone", phone);
        userData.put("createTime", DateUtils.format(DateUtils.now()));
        userData.put("id", CryptoUtils.generateUUIDWithoutHyphens());
        
        // 转换为JSON
        String userJson = JsonUtils.toJson(userData);
        assertNotNull(userJson);
        System.out.println("用户数据JSON: " + userJson);
        
        // 验证密码
        assertTrue(CryptoUtils.verifySha256WithSalt(password, salt, hashedPassword));
        
        // 掩码敏感信息
        String maskedPhone = StringUtils.maskPhone(phone);
        String maskedEmail = StringUtils.maskEmail(email);
        System.out.println("掩码手机号: " + maskedPhone);
        System.out.println("掩码邮箱: " + maskedEmail);
        
        System.out.println("工具类集成测试通过");
    }
}
