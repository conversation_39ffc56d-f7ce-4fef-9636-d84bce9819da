# RSocket服务使用说明

## 服务概述

本项目已成功启动了一个RSocket服务，提供了高性能的响应式通信能力。

## 服务信息

- **RSocket服务端口**: 7000 (TCP)
- **Web服务端口**: 8081 (HTTP)
- **应用上下文路径**: /wyj202506

## 可用的RSocket端点

### 1. Request-Response 模式
- **路由**: `request-response`
- **描述**: 客户端发送一个请求，服务器返回一个响应
- **示例响应**: `Echo: [你的消息] at [当前时间]`

### 2. Request-Stream 模式
- **路由**: `request-stream`
- **描述**: 客户端发送一个请求，服务器返回一个数据流
- **示例响应**: 每秒返回一个响应，共10个
- **响应格式**: `Stream response [序号] for request: [你的消息] at [当前时间]`

### 3. Fire-and-Forget 模式
- **路由**: `fire-and-forget`
- **描述**: 客户端发送一个请求，不期望响应
- **行为**: 服务器会在控制台打印接收到的消息

### 4. Channel 模式
- **路由**: `channel`
- **描述**: 双向流通信
- **行为**: 服务器会对每个接收到的消息返回一个响应

## 如何测试RSocket服务

### 使用RSocket CLI工具

1. 安装RSocket CLI:
```bash
npm install -g rsocket-cli
```

2. 测试Request-Response:
```bash
rsocket-cli --request --data "Hello RSocket" --route request-response tcp://localhost:7000
```

3. 测试Request-Stream:
```bash
rsocket-cli --stream --data "Stream Request" --route request-stream tcp://localhost:7000
```

4. 测试Fire-and-Forget:
```bash
rsocket-cli --fnf --data "Fire and Forget Message" --route fire-and-forget tcp://localhost:7000
```

### 使用Java客户端

可以创建一个Java客户端来连接RSocket服务：

```java
RSocket socket = RSocketConnector.create()
    .connect(TcpClientTransport.create("localhost", 7000))
    .block();

// Request-Response
String response = socket
    .requestResponse(DefaultPayload.create("Hello", "request-response"))
    .map(payload -> payload.getDataUtf8())
    .block();
```

## 服务状态

✅ **RSocket服务器**: 已在端口7000启动  
✅ **Web服务器**: 已在端口8081启动  
✅ **数据库连接**: 已成功连接到MySQL  
✅ **应用程序**: 启动完成，运行正常  

## 注意事项

1. 确保端口7000和8081没有被其他应用程序占用
2. 数据库连接需要MySQL服务运行在localhost:3306
3. 使用RSocket客户端时需要指定正确的路由路径
4. 所有的RSocket交互都是异步和响应式的

## 日志监控

应用程序会在控制台输出详细的日志信息，包括：
- RSocket连接状态
- 接收到的消息
- 发送的响应
- 错误信息

服务现在已经准备好接收RSocket连接和处理请求！
